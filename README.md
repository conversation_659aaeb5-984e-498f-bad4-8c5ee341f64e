# MACD Advanced Trading Strategy for MetaTrader 5

A comprehensive Python-based trading strategy system that implements MACD (Moving Average Convergence Divergence) analysis across multiple timeframes with AI enhancement capabilities.

## Features

### Core Strategy Components
- **Multi-timeframe Analysis**: 1H, 4H, and Daily timeframe confluence
- **MACD Signal Generation**: Advanced crossover detection with histogram analysis
- **Trend Analysis**: Higher Highs/Higher Lows pattern recognition
- **AI Enhancement**: LSTM neural network for signal confidence scoring
- **Risk Management**: Position sizing, stop-loss, and take-profit calculation
- **Performance Tracking**: Comprehensive trade analysis and reporting

### Technical Indicators
- MACD (12, 26, 9) with histogram analysis
- RSI (14) for momentum confirmation
- ATR (14) for volatility-based stop losses
- Support/Resistance level identification
- Market structure analysis

### Risk Management
- 1% risk per trade (configurable)
- ATR-based stop losses
- Risk-reward ratio optimization
- Correlation risk assessment
- Maximum drawdown protection

## Installation

### Prerequisites
- Python 3.8 or higher
- MetaTrader 5 terminal installed and running
- Demo or live trading account

### Dependencies Installation

```bash
# Install required packages
pip install -r requirements.txt
```

**Note**: TA-Lib installation may require additional steps on Windows:
```bash
# For Windows users, download the appropriate wheel from:
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.25-cp39-cp39-win_amd64.whl  # Adjust for your Python version
```

### Alternative TA-Lib Installation
If TA-Lib installation fails, the system will automatically use custom implementations of technical indicators.

## Configuration

### Basic Setup
1. Ensure MetaTrader 5 is running and logged into your account
2. Add "DEX900 DOWN" symbol to Market Watch (or modify `SYMBOL` in `config/settings.py`)
3. Adjust risk parameters in `config/settings.py` if needed

### Key Configuration Parameters
```python
# Trading Symbol
SYMBOL = "DEX900 DOWN"

# Risk Management
RISK_PERCENTAGE = 0.01  # 1% risk per trade
MAX_SPREAD = 10  # Maximum spread in points

# MACD Parameters
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9
```

## Usage

### Running the Strategy

#### Simulation Mode (Recommended for Testing)
```bash
python main.py
```

#### Live Trading Mode
Modify `main.py` to set `simulation_mode=False`:
```python
strategy = MACDTradingStrategy(simulation_mode=False)
```

### Running Tests
```bash
python -m pytest tests/ -v
```

### Single Test Run
```bash
python tests/test_strategy.py
```

## Strategy Logic

### Signal Generation Process

1. **Daily Trend Analysis**
   - Determines overall market direction using HH/HL analysis
   - MACD position relative to zero line
   - Trend strength calculation

2. **4H MACD Crossover**
   - Detects bullish/bearish MACD crossovers
   - Validates existing trend continuation
   - Filters false signals

3. **1H Confirmation**
   - Histogram growing in size
   - Histogram color alignment
   - Volume confirmation (if available)

4. **AI Enhancement**
   - LSTM model predicts signal confidence
   - Uses MACD components as features
   - Provides 0-1 confidence score

### Trade Execution

1. **Entry Conditions**
   - Daily trend alignment
   - 4H MACD crossover
   - 1H histogram confirmation
   - AI confidence > threshold

2. **Risk Management**
   - ATR-based stop loss calculation
   - 1% account risk per trade
   - 2:1 risk-reward ratio
   - Spread and margin validation

3. **Position Management**
   - Automatic stop loss and take profit
   - Position size optimization
   - Correlation risk assessment

## Project Structure

```
MCAdvancedTrader_V1.2 - D9D/
├── main.py                    # Main strategy execution
├── requirements.txt           # Dependencies
├── config/
│   └── settings.py           # Configuration parameters
├── strategy/
│   ├── indicators.py         # Technical indicators
│   ├── signals.py           # Signal generation
│   ├── trend_analysis.py    # Trend determination
│   └── ai_enhancement.py    # LSTM model
├── trading/
│   ├── mt5_connector.py     # MT5 connection
│   ├── risk_manager.py      # Risk management
│   └── order_manager.py     # Order execution
├── utils/
│   ├── logger.py           # Logging system
│   └── performance.py      # Performance tracking
├── tests/
│   └── test_strategy.py    # Unit tests
└── logs/                   # Log files
```

## Performance Monitoring

### Automatic Logging
- All trades logged to `logs/trades.log`
- Performance metrics in `logs/performance.log`
- Strategy execution in `logs/macd_strategy.log`

### Performance Reports
```python
# Generate comprehensive performance report
strategy.performance_tracker.generate_report()
```

### Key Metrics Tracked
- Win rate and profit factor
- Sharpe ratio and maximum drawdown
- Average win/loss ratios
- Consecutive wins/losses
- Monthly performance breakdown

## Customization

### Modifying Strategy Parameters
Edit `config/settings.py` to adjust:
- MACD periods (fast, slow, signal)
- Risk percentage per trade
- Timeframes for analysis
- AI model parameters

### Adding New Indicators
1. Add indicator calculation to `strategy/indicators.py`
2. Update signal generation logic in `strategy/signals.py`
3. Modify AI features in `strategy/ai_enhancement.py`

### Custom Risk Rules
Extend `trading/risk_manager.py` to add:
- Custom position sizing rules
- Additional validation checks
- Portfolio-level risk management

## Troubleshooting

### Common Issues

1. **MT5 Connection Failed**
   - Ensure MT5 is running and logged in
   - Check if algorithmic trading is enabled
   - Verify symbol availability

2. **TA-Lib Import Error**
   - Install TA-Lib using wheel file for Windows
   - System will use custom implementations as fallback

3. **TensorFlow/AI Issues**
   - AI enhancement will be disabled if TensorFlow unavailable
   - Strategy will continue without AI features

4. **Symbol Not Found**
   - Verify "DEX900 DOWN" is in Market Watch
   - Update SYMBOL in config/settings.py if using different symbol

### Debug Mode
Enable detailed logging by setting `LOG_LEVEL = 'DEBUG'` in `config/settings.py`.

## Disclaimer

This trading strategy is for educational and research purposes. Past performance does not guarantee future results. Always test thoroughly on demo accounts before live trading. Trading involves substantial risk of loss.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files in the `logs/` directory
3. Run unit tests to verify system integrity
4. Ensure all dependencies are properly installed
